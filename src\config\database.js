import { Client, Pool } from 'pg'
import { env } from '../utils/env'
import AppError from '../common/errors/AppError'

const isDebug = env('APP_DEBUG', 'true') === 'true'

// Database configuration
const dbConfig = {
  host: env('DB_HOST', 'localhost'),
  port: parseInt(env('DB_PORT', '5432')),
  user: env('DB_USER', 'postgres'),
  password: env('DB_PASSWORD', ''),
  database: env('DB_NAME', 'tasks_db'),
  ssl: env('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false
}

// Connection pool for the application
let pool = null

// Get database connection pool
export const getPool = () => {
  if (!pool) {
    throw new AppError(
      'Database not initialized. Call initializeDatabase first.'
    )
  }
  return pool
}

// Execute a query with the pool
export const query = async (text, params = []) => {
  const client = getPool()
  try {
    const result = await client.query(text, params)
    return result
  } catch (error) {
    if (isDebug) {
      console.error('Database query error:', error)
      console.error('Query:', text)
      console.error('Params:', params)
    }
    throw error
  }
}

// Check if database exists
const checkDatabaseExists = async (dbName) => {
  const client = new Client({
    ...dbConfig,
    database: 'postgres' // Connect to default postgres database
  })

  try {
    await client.connect()
    const result = await client.query(
      'SELECT 1 FROM pg_database WHERE datname = $1',
      [dbName]
    )
    return result.rows.length > 0
  } catch (error) {
    throw new AppError(`Error checking database existence: ${error.message}`)
  } finally {
    await client.end()
  }
}

// Create database if it doesn't exist
const createDatabase = async (dbName) => {
  const client = new Client({
    ...dbConfig,
    database: 'postgres' // Connect to default postgres database
  })

  try {
    await client.connect()
    await client.query(`CREATE DATABASE "${dbName}"`)
    if (isDebug) {
      console.log(`Database "${dbName}" created successfully`)
    }
  } catch (error) {
    throw new AppError(`Error creating database: ${error.message}`)
  } finally {
    await client.end()
  }
}

// Create tasks table
const createTasksTable = async () => {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS tasks (
      id SERIAL PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      completed BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `

  try {
    await query(createTableQuery)
    if (isDebug) {
      console.log('Tasks table created/verified successfully')
    }
  } catch (error) {
    throw new AppError(`Error creating tasks table: ${error.message}`)
  }
}

// Create trigger for updated_at column
const createUpdatedAtTrigger = async () => {
  const createTriggerFunction = `
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = CURRENT_TIMESTAMP;
      RETURN NEW;
    END;
    $$ language 'plpgsql'
  `

  const createTrigger = `
    DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
    CREATE TRIGGER update_tasks_updated_at
      BEFORE UPDATE ON tasks
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column()
  `

  try {
    await query(createTriggerFunction)
    await query(createTrigger)
    if (isDebug) {
      console.log('Updated_at trigger created successfully')
    }
  } catch (error) {
    throw new AppError(`Error creating updated_at trigger: ${error.message}`)
  }
}

// Test database connection
const testConnection = async () => {
  try {
    const result = await query('SELECT NOW() as current_time')
    if (isDebug) {
      console.log(
        'Database connection test successful:',
        result.rows[0].current_time
      )
    }
  } catch (error) {
    throw new AppError(`Database connection test failed: ${error.message}`)
  }
}

// Initialize database
export const initializeDatabase = async () => {
  try {
    if (isDebug) {
      console.log('Starting database initialization...')
    }

    // Check if database exists, create if it doesn't
    const dbExists = await checkDatabaseExists(dbConfig.database)
    if (!dbExists) {
      if (isDebug) {
        console.log(
          `Database "${dbConfig.database}" does not exist. Creating...`
        )
      }
      await createDatabase(dbConfig.database)
    } else if (isDebug) {
      console.log(`Database "${dbConfig.database}" already exists`)
    }

    // Create connection pool
    pool = new Pool(dbConfig)

    // Handle pool errors
    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err)
      process.exit(-1)
    })

    // Test the connection
    await testConnection()

    // Create tables and triggers
    await createTasksTable()
    await createUpdatedAtTrigger()

    if (isDebug) {
      console.log('Database initialization completed successfully')
    }
  } catch (err) {
    console.error('Database initialization failed:', err.message)
    throw new AppError(`Error initializing database: ${err.message}`)
  }
}

// Close database connection
export const closeDatabase = async () => {
  if (pool) {
    await pool.end()
    pool = null
    if (isDebug) {
      console.log('Database connection closed')
    }
  }
}
