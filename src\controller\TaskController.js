export const getAllTasks = async (req, res, next) => {
  return res.response({
    message: 'getAllTasks'
  })
}

export const getTask = async (req, res, next) => {
  return res.response({
    message: 'getTask'
  })
}

export const createTask = async (req, res, next) => {
  return res.response({
    message: 'createTask'
  })
}

export const updateTask = async (req, res, next) => {
  return res.response({
    message: 'updateTask'
  })
}

export const deleteTask = async (req, res, next) => {
  return res.response({
    message: 'deleteTask'
  })
}
