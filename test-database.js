import 'dotenv/config'
import { initializeDatabase, query, closeDatabase } from './src/config/database.js'

async function testDatabase() {
  try {
    console.log('Testing database connection and initialization...')
    
    // Initialize database
    await initializeDatabase()
    
    // Test basic query
    const result = await query('SELECT NOW() as current_time, version() as pg_version')
    console.log('Database test successful!')
    console.log('Current time:', result.rows[0].current_time)
    console.log('PostgreSQL version:', result.rows[0].pg_version)
    
    // Test tasks table
    const tableCheck = await query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'tasks' 
      ORDER BY ordinal_position
    `)
    
    console.log('\nTasks table structure:')
    tableCheck.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`)
    })
    
    // Test insert
    const insertResult = await query(
      'INSERT INTO tasks (title, description) VALUES ($1, $2) RETURNING *',
      ['Test Task', 'This is a test task']
    )
    console.log('\nInserted test task:', insertResult.rows[0])
    
    // Test select
    const selectResult = await query('SELECT * FROM tasks WHERE id = $1', [insertResult.rows[0].id])
    console.log('Retrieved task:', selectResult.rows[0])
    
    // Clean up test data
    await query('DELETE FROM tasks WHERE id = $1', [insertResult.rows[0].id])
    console.log('Test data cleaned up')
    
    console.log('\n✅ All database tests passed!')
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message)
    console.error(error.stack)
  } finally {
    await closeDatabase()
    console.log('Database connection closed')
  }
}

testDatabase()
